const fetch = require("node-fetch");

const SOLVECAPTCHA_API_KEY = "66b4447f6efdc82957de2ae421659608";
const SITE_KEY = "0x4AAAAAABzweCbOQd_LmvY9";
const WEBSITE_URL = "https://blast.fun";

/**
 * Solve different types of captchas using solvecaptcha service
 * @param {string} captchaType - Type of captcha: 'turnstile', 'recaptchav2', 'recaptchav3'
 * @param {string} websiteURL - The URL where the captcha is located
 * @param {string} websiteKey - The site key for the captcha
 * @param {Object} options - Additional options for specific captcha types
 * @returns {Promise<string>} - The captcha solution token
 */
async function solveCaptcha(captchaType, websiteURL, websiteKey, options = {}) {
  try {
    let taskConfig;

    switch (captchaType.toLowerCase()) {
      case "turnstile":
        taskConfig = {
          key: SOLVECAPTCHA_API_KEY,
          method: "turnstile",
          task: {
            type: "TurnstileTaskProxyless",
            websiteURL: websiteURL,
            websiteKey: websiteKey,
          },
        };
        break;

      case "recaptchav2":
        taskConfig = {
          key: SOLVECAPTCHA_API_KEY,
          method: "userrecaptcha",
          task: {
            type: "NoCaptchaTaskProxyless",
            websiteURL: websiteURL,
            websiteKey: websiteKey,
          },
        };
        break;

      case "recaptchav3":
        taskConfig = {
          key: SOLVECAPTCHA_API_KEY,
          method: "userrecaptcha",
          task: {
            type: "RecaptchaV3TaskProxyless",
            websiteURL: websiteURL,
            websiteKey: websiteKey,
            minScore: options.minScore || 0.3,
            pageAction: options.pageAction || "verify",
          },
        };
        break;

      default:
        throw new Error(`Unsupported captcha type: ${captchaType}`);
    }

    // Step 1: Submit the captcha task
    console.log(`Submitting ${captchaType} captcha task...`);
    const createTask = await fetch("https://api.solvecaptcha.com/in.php", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(taskConfig),
    });

    const createTaskResponse = await createTask.json();

    if (createTaskResponse.errorId !== 0) {
      throw new Error(
        `Failed to create task: ${createTaskResponse.errorDescription}`
      );
    }

    const { taskId } = createTaskResponse;
    console.log(`Task created with ID: ${taskId}`);

    // Step 2: Poll for the solution
    const maxTry = 120; // 2 minutes timeout
    let tryCount = 0;

    while (tryCount < maxTry) {
      const response = await fetch(
        "https://api.solvecaptcha.com/getTaskResult",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ clientKey: SOLVECAPTCHA_API_KEY, taskId }),
        }
      );

      const result = await response.json();

      if (result.errorId !== 0) {
        throw new Error(
          `Error getting task result: ${result.errorDescription}`
        );
      }

      if (result.status === "ready") {
        console.log(`${captchaType} captcha solved successfully!`);
        return result.solution.token;
      }

      if (result.status === "processing") {
        console.log(`Attempt ${tryCount + 1}/${maxTry}: Still processing...`);
      }

      tryCount++;
      await new Promise((r) => setTimeout(r, 1000)); // Wait 1 second
    }

    throw new Error(`Timeout: Captcha not solved within ${maxTry} seconds`);
  } catch (error) {
    console.error("Error solving captcha:", error.message);
    throw error;
  }
}
