const fetch = require("node-fetch");

const SOLVECAPTCHA_API_KEY = "66b4447f6efdc82957de2ae421659608";
const SITE_KEY = "0x4AAAAAABzweCbOQd_LmvY9";
const WEBSITE_URL = "https://blast.fun";

async function solveTurnstileCaptcha(websiteURL, websiteKey, options = {}) {
  try {
    const submitData = {
      key: SOLVECAPTCHA_API_KEY,
      method: "turnstile",
      sitekey: websiteKey,
      pageurl: websiteURL,
    };

    console.log(`Submitting Turnstile captcha for ${websiteURL}...`);

    const submitResponse = await fetch("https://api.solvecaptcha.com/in.php", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams(submitData),
    });

    const submitText = await submitResponse.text();
    console.log("Submit response:", submitText);

    if (submitText.startsWith("ERROR")) {
      throw new Error(`Failed to submit captcha: ${submitText}`);
    }

    if (!submitText.startsWith("OK|")) {
      throw new Error(`Unexpected submit response: ${submitText}`);
    }

    const captchaId = submitText.split("|")[1];

    await new Promise((resolve) => setTimeout(resolve, 20000));

    const maxAttempts = 24; // 2 minutes total (24 * 5 seconds)
    let attempts = 0;

    while (attempts < maxAttempts) {
      const resultResponse = await fetch(
        `https://api.solvecaptcha.com/res.php?key=${SOLVECAPTCHA_API_KEY}&action=get&id=${captchaId}`
      );
      console.l

      const resultText = await resultResponse.text();
      console.log(`Attempt ${attempts + 1}: ${resultText}`);

      if (resultText === "CAPCHA_NOT_READY") {
        attempts++;
        if (attempts < maxAttempts) {
          console.log("Captcha not ready, waiting 5 seconds...");
          await new Promise((resolve) => setTimeout(resolve, 5000));
        }
        continue;
      }

      if (resultText.startsWith("ERROR")) {
        throw new Error(`Error getting result: ${resultText}`);
      }

      if (resultText.startsWith("OK|")) {
        const token = resultText.split("|")[1];
        console.log("✅ Turnstile captcha solved successfully!");
        return token;
      }

      throw new Error(`Unexpected result response: ${resultText}`);
    }

    throw new Error(
      `Timeout: Captcha not solved within ${maxAttempts * 5} seconds`
    );
  } catch (error) {
    console.error("❌ Error solving Turnstile captcha:", error.message);
    throw error;
  }
}

// Legacy function - now uses the new solveTurnstileCaptcha function
async function getTurnstileToken() {
  return await solveTurnstileCaptcha(WEBSITE_URL, SITE_KEY);
}

async function getBalance() {
  try {
    const response = await fetch(
      `https://api.solvecaptcha.com/res.php?key=${SOLVECAPTCHA_API_KEY}&action=getbalance`
    );
    const result = await response.text();
    console.log("Account balance:", result);
    return result;
  } catch (error) {
    console.error("Error getting balance:", error.message);
  }
}

// Usage examples:
async function examples() {
  try {
    const token1 = await getTurnstileToken();
    console.log("✅ Turnstile token:", token1);
  } catch (error) {
    console.error("❌ Example failed:", error.message);
  }
}

// Main execution
async function main() {
  console.log("🚀 Starting Turnstile captcha solver...");
  console.log("📋 API Key:", SOLVECAPTCHA_API_KEY.substring(0, 8) + "...");
  console.log("🌐 Website:", WEBSITE_URL);
  console.log("🔑 Site Key:", SITE_KEY);
  console.log("💰 Rate: $0.8 per 1000 captchas");
  console.log("⏱️  Average solving time: ~15 seconds\n");

  await examples();
}

// Run the main function
main();
