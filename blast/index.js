const fetch = require("node-fetch");

const SOLVECAPTCHA_API_KEY = "66b4447f6efdc82957de2ae421659608";
const SITE_KEY = "0x4AAAAAABzweCbOQd_LmvY9";
const WEBSITE_URL = "https://blast.fun";

async function solveTurnstileCaptcha(websiteURL, websiteKey) {
  try {
    const taskConfig = {
      key: SOLVECAPTCHA_API_KEY,
      method: "turnstile",
      task: {
        type: "TurnstileTaskProxyless",
        websiteURL: websiteURL,
        websiteKey: websiteKey,
      },
    };

    console.log("Submitting Turnstile captcha task...");
    const createTask = await fetch("https://api.solvecaptcha.com/in.php", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(taskConfig),
    });

    const createTaskResponse = await createTask.json();

    if (createTaskResponse.errorId !== 0) {
      throw new Error(
        `Failed to create task: ${createTaskResponse.errorDescription}`
      );
    }

    const { taskId } = createTaskResponse;

    // Step 2: Poll for the solution
    const maxTry = 120; // 2 minutes timeout
    let tryCount = 0;

    while (tryCount < maxTry) {
      const response = await fetch(
        "https://api.solvecaptcha.com/getTaskResult",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ clientKey: SOLVECAPTCHA_API_KEY, taskId }),
        }
      );

      const result = await response.json();

      if (result.errorId !== 0) {
        throw new Error(
          `Error getting task result: ${result.errorDescription}`
        );
      }

      if (result.status === "ready") {
        console.log("Turnstile captcha solved successfully!");
        return result.solution.token;
      }

      if (result.status === "processing") {
        console.log(`Attempt ${tryCount + 1}/${maxTry}: Still processing...`);
      }

      tryCount++;
      await new Promise((r) => setTimeout(r, 1000)); // Wait 1 second
    }

    throw new Error(`Timeout: Captcha not solved within ${maxTry} seconds`);
  } catch (error) {
    console.error("Error solving Turnstile captcha:", error.message);
    throw error;
  }
}

// Legacy function - now uses the new solveTurnstileCaptcha function
async function getTurnstileToken() {
  return await solveTurnstileCaptcha(WEBSITE_URL, SITE_KEY);
}

async function examples() {
  try {
    // Example 1: Using the legacy function
    const token1 = await getTurnstileToken();
    console.log("Turnstile token (legacy):", token1);

    // Example 2: Using the new function directly
    const token2 = await solveTurnstileCaptcha(
      "https://blast.fun",
      "0x4AAAAAABzweCbOQd_LmvY9"
    );
    console.log("Turnstile token (direct):", token2);
  } catch (error) {
    console.error("Failed to solve captcha:", error.message);
  }
}

// Run the example
getTurnstileToken();
