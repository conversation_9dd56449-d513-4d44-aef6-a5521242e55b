const fetch = require("node-fetch");

const SOLVECAPTCHA_API_KEY = "66b4447f6efdc82957de2ae421659608";

async function getTurnstileToken() {
  const createTask = await fetch("https://api.solvecaptcha.com/in.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      key: SOLVECAPTCHA_API_KEY,
      method: "turnstile",
      sitekey: '0x4AAAAAABzweCbOQd_LmvY9',
      task: {
        type: "TurnstileTaskProxyless",
        websiteURL: "https://blast.fun",
        websiteKey: "0x4AAAAAABzweCbOQd_LmvY9", // We must extract this
      },
    }),
  });

  const { taskId } = await createTask.json();
  const maxTry = 100;
  let tryCount = 0;

  while (tryCount < maxTry) {
    const response = await fetch("https://api.solvecaptcha.com/getTaskResult", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ clientKey: SOLVECAPTCHA_API_KEY, taskId }),
    });

    const result = await response.json();
    if (result.status === "ready") {
      console.log("Turnstile token:", result.solution.token);
      return result.solution.token; // This is the valid turnstileToken
    }
    tryCount++;
    await new Promise((r) => setTimeout(r, 1000));
  }
}

getTurnstileToken();
